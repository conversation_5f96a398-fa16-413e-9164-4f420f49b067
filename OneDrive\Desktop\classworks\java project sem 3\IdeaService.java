// IdeaService.java
import java.io.*;
import java.util.*;

public class IdeaService {
    private static final String FILE_NAME = "ideas.txt";

    public void addIdea(Idea idea) throws IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter(FILE_NAME, true));
        bw.write(idea.toFileString());
        bw.newLine();
        bw.close();
    }

    public List<Idea> getAllIdeas() throws IOException {
        List<Idea> ideas = new ArrayList<>();
        File file = new File(FILE_NAME);
        if (!file.exists() || file.length() == 0) return ideas;

        BufferedReader br = new BufferedReader(new FileReader(FILE_NAME));
        String line;
        while ((line = br.readLine()) != null) {
            ideas.add(Idea.fromFileString(line));
        }
        br.close();
        return ideas;
    }

    public List<Idea> searchIdeas(String keyword) throws IOException {
        List<Idea> allIdeas = getAllIdeas();
        List<Idea> result = new ArrayList<>();
        for (Idea idea : allIdeas) {
            if (idea.toFileString().toLowerCase().contains(keyword.toLowerCase())) {
                result.add(idea);
            }
        }
        return result;
    }

    public int getNextId() throws IOException {
        List<Idea> allIdeas = getAllIdeas();
        if (allIdeas.isEmpty()) return 1;
        return allIdeas.get(allIdeas.size() - 1).getId() + 1;
    }
}