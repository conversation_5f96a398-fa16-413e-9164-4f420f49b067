public class Idea{
    private int id;
    private String title,problem,solution,targetUsers,category;
    public Idea(int id, String title, String problem, String solution, String targetUsers, String category) {
        this.id = id;
        this.title = title;
        this.problem = problem;
        this.solution = solution;
        this.targetUsers = targetUsers;
        this.category = category;
    }

    public String toFileString(){
        return String.format("%-4s %-15s %-20s %-20s %-12s %-15s", id, title, problem, solution, targetUsers, category);
    }
    
    public void display(){
        System.out.println(toFileString());
    }
    public int getId(){
        return id;
    }
}