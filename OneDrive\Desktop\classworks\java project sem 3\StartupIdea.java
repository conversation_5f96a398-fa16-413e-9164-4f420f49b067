/**
 * Entity class representing a startup idea
 * Contains all the properties and methods related to a single startup idea
 */
public class StartupIdea {
    // Instance variables
    private int id;
    private String title;
    private String problem;
    private String solution;
    private String category;
    private String targetUsers;

    /**
     * Constructor to create a new StartupIdea
     * @param id Unique identifier for the idea
     * @param title Title of the startup idea
     * @param problem Problem that the idea addresses
     * @param solution Proposed solution
     * @param category Category/domain of the idea
     * @param targetUsers Target user group
     */
    public StartupIdea(int id, String title, String problem, String solution, String category, String targetUsers) {
        this.id = id;
        this.title = title;
        this.problem = problem;
        this.solution = solution;
        this.category = category;
        this.targetUsers = targetUsers;
    }

    // Getter methods
    public int getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public String getProblem() {
        return problem;
    }

    public String getSolution() {
        return solution;
    }

    public String getCategory() {
        return category;
    }

    public String getTargetUsers() {
        return targetUsers;
    }

    // Setter methods
    public void setId(int id) {
        this.id = id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setProblem(String problem) {
        this.problem = problem;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public void setTargetUsers(String targetUsers) {
        this.targetUsers = targetUsers;
    }

    /**
     * Converts the StartupIdea object to a string format suitable for file storage
     * Uses pipe (|) as delimiter between fields
     * @return String representation for file storage
     */
    public String toFileString() {
        return id + "|" + title + "|" + problem + "|" + solution + "|" + category + "|" + targetUsers;
    }

    /**
     * Creates a StartupIdea object from a file string
     * Parses pipe-delimited string back into object
     * @param line Pipe-delimited string from file
     * @return StartupIdea object
     */
    public static StartupIdea fromFileString(String line) {
        String[] parts = line.split("\\|");
        if (parts.length != 6) {
            throw new IllegalArgumentException("Invalid file format. Expected 6 fields separated by |");
        }
        return new StartupIdea(
            Integer.parseInt(parts[0]), 
            parts[1], 
            parts[2], 
            parts[3], 
            parts[4], 
            parts[5]
        );
    }

    /**
     * Displays the startup idea in a formatted way
     */
    public void display() {
        System.out.println("ID: " + id);
        System.out.println("Title: " + title);
        System.out.println("Problem: " + problem);
        System.out.println("Solution: " + solution);
        System.out.println("Category: " + category);
        System.out.println("Target Users: " + targetUsers);
        System.out.println("-------------------------");
    }

    /**
     * String representation of the object for debugging
     * @return String representation
     */
    @Override
    public String toString() {
        return "StartupIdea{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", problem='" + problem + '\'' +
                ", solution='" + solution + '\'' +
                ", category='" + category + '\'' +
                ", targetUsers='" + targetUsers + '\'' +
                '}';
    }

    /**
     * Checks if this idea matches a search keyword in title or category
     * @param keyword Search keyword (case-insensitive)
     * @return true if matches, false otherwise
     */
    public boolean matchesKeyword(String keyword) {
        String lowerKeyword = keyword.toLowerCase();
        return title.toLowerCase().contains(lowerKeyword) || 
               category.toLowerCase().contains(lowerKeyword);
    }
}
