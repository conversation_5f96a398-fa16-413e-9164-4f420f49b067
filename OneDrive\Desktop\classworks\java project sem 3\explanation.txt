STARTUP IDEA VAULT - PROJECT EXPLANATION
==========================================

PROJECT OVERVIEW:
This Java application is a console-based Startup Idea Management System that allows users to store, view, and search startup ideas. The application uses file-based storage to persist data.

SYSTEM ARCHITECTURE:
===================

1. ENTITY CLASSES:

   a) Idea.java
      - Represents a single startup idea
      - Fields: id (int), title (String), problem (String), solution (String), targetUsers (String), category (String)
      - Methods: toFileString(), display(), getId()
      - Uses formatted string output for file storage

   b) StartupIdea.java (Alternative implementation)
      - More comprehensive entity class with full getter/setter methods
      - Includes additional methods: fromFileString(), matchesKeyword(), toString()
      - Uses pipe-delimited format for file storage

2. SERVICE LAYER:

   IdeaService.java
   - Contains business logic for CRUD operations
   - Methods:
     * addIdea(Scanner) - Collects user input and saves new idea
     * viewAllIdeas() - Displays all stored ideas in formatted table
     * searchIdea(Scanner) - Searches ideas by keyword in title/category
     * getNextId() - Generates unique ID for new ideas
   - Handles file I/O operations with ideas.txt

3. APPLICATION LAYER:

   a) IdeaApp.java
      - Main application using IdeaService
      - Provides console menu interface
      - Menu options: Add New Idea, View All Ideas, Search Idea, Exit

   b) StartupIdeaVault.java (Alternative main class)
      - Self-contained application with embedded business logic
      - Similar functionality but without separate service layer

DATA STORAGE:
============
- File: ideas.txt
- Format: Space-separated values for Idea class OR Pipe-delimited for StartupIdea class
- Fields stored: ID, Title, Problem, Solution, Target Users, Category

FEATURES:
=========
1. Add New Ideas - Collects idea details from user input
2. View All Ideas - Displays formatted list of all stored ideas
3. Search Ideas - Find ideas by keyword matching in title or category
4. Persistent Storage - Ideas saved to file and loaded on application restart
5. Auto ID Generation - Automatically assigns unique IDs to new ideas

USAGE INSTRUCTIONS:
==================
1. Compile: javac IdeaApp.java (compiles all dependent classes)
2. Run: java IdeaApp
3. Follow menu prompts to manage startup ideas
4. Data is automatically saved to ideas.txt file

PROJECT STRUCTURE:
=================
- Idea.java - Basic entity class
- StartupIdea.java - Enhanced entity class (alternative)
- IdeaService.java - Business logic service
- IdeaApp.java - Main application
- StartupIdeaVault.java - Alternative self-contained application
- ideas.txt - Data storage file (created automatically)
