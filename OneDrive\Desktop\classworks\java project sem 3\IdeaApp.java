import java.io.IOException;

public class IdeaApp{
    public static void main(String[] args)throws IOException{
        Scanner sc = new Scanner(System.in);
        IdeaService service = new IdeaService();
        int choice;
        do { 
            System.out.println("\n ............PROJECT NOTION APP............");
            System.out.println("1. Add New Idea");
            System.out.println("2. View All Ideas");
            System.out.println("3. Search Idea");
            System.out.println("4. Exit");
            System.out.print("Enter your choice: ");
            choice = Integer.parseInt(sc.nextLine());

            switch (choice) {
                case 1:
                    service.addIdea(sc);
                    break;
                case 2:
                    service.viewAllIdeas();
                    break;
                case 3:
                    service.searchIdea(sc);
                    break;
                case 4:
                    System.out.println("Exiting. Stay innovative!");
                    break;
                default:
                    System.out.println("Invalid choice. Try again.");
            }

        } 
    }
}